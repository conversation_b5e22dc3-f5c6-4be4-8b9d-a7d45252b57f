#!/bin/bash

# Shell脚本：处理CSV文件和Syslog日志文件
# 功能1：读取CSV文件并统计行数
# 功能2：读取Syslog文件中包含'5 [BizEnv7]'的行

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OUT_FILE="$SCRIPT_DIR/out"

# 清空输出文件
> "$OUT_FILE"

# 功能1：处理CSV文件
process_csv() {
    local csv_file="$1"
    
    if [ -f "$csv_file" ]; then
        echo "=== CSV文件处理结果 ===" >> "$OUT_FILE"
        echo "CSV文件路径: $csv_file" >> "$OUT_FILE"
        
        # 统计CSV文件行数
        line_count=$(wc -l < "$csv_file")
        echo "CSV文件总行数: $line_count" >> "$OUT_FILE"
        echo "" >> "$OUT_FILE"
    else
        echo "CSV文件不存在: $csv_file" >> "$OUT_FILE"
        echo "" >> "$OUT_FILE"
    fi
}

# 功能2：处理Syslog文件
process_syslog() {
    local syslog_file="$1"
    
    if [ -f "$syslog_file" ]; then
        echo "=== Syslog文件处理结果 ===" >> "$OUT_FILE"
        echo "Syslog文件路径: $syslog_file" >> "$OUT_FILE"
        echo "包含'5 [BizEnv7]'的行:" >> "$OUT_FILE"
        echo "----------------------------------------" >> "$OUT_FILE"
        
        # 查找包含'5 [BizEnv7]'的行并输出到out文件
        grep "5 \[BizEnv7\]" "$syslog_file" >> "$OUT_FILE"
        
        echo "----------------------------------------" >> "$OUT_FILE"
        echo "" >> "$OUT_FILE"
    else
        echo "Syslog文件不存在: $syslog_file" >> "$OUT_FILE"
        echo "" >> "$OUT_FILE"
    fi
}

# 主函数
main() {
    # 检查参数数量
    if [ $# -ne 2 ]; then
        echo "使用方法: $0 <CSV文件路径> <Syslog文件路径>"
        echo "示例: $0 /path/to/data.csv /path/to/Syslog.log"
        exit 1
    fi
    
    local csv_file="$1"
    local syslog_file="$2"
    
    echo "开始处理文件..." >> "$OUT_FILE"
    echo "处理时间: $(date)" >> "$OUT_FILE"
    echo "" >> "$OUT_FILE"
    
    # 处理CSV文件
    process_csv "$csv_file"
    
    # 处理Syslog文件
    process_syslog "$syslog_file"
    
    echo "处理完成！结果已保存到: $OUT_FILE"
}

# 执行主函数
main "$@"
