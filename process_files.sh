#!/bin/bash

# Shell脚本：处理CSV文件和Syslog日志文件
# 功能1：读取指定的CSV文件并统计行数
# 功能2：读取Syslog文件中包含'5 [BizEnv7]'的行

# CSV文件配置数组
# 格式1: 简单文件名 - "filename.csv"
# 格式2: 带条件筛选 - "filename.csv,column1,value1,column2,value2"
CMP_CSVS[0]="1.csv"
CMP_CSVS[1]="2.csv,aa,1,bb,2"
# 可以继续添加更多CSV文件
# CMP_CSVS[2]="3.csv"
# CMP_CSVS[3]="4.csv,name,john,age,25"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OUT_FILE="$SCRIPT_DIR/out"

# 清空输出文件
> "$OUT_FILE"

# 解析CSV配置并处理条件筛选
parse_csv_config() {
    local config="$1"
    local csv_dir="$2"

    # 分割配置字符串
    IFS=',' read -ra parts <<< "$config"
    local csv_filename="${parts[0]}"
    local csv_file="$csv_dir/$csv_filename"

    echo "文件名: $csv_filename" >> "$OUT_FILE"
    echo "完整路径: $csv_file" >> "$OUT_FILE"

    if [ ! -f "$csv_file" ]; then
        echo "状态: 文件不存在" >> "$OUT_FILE"
        echo "----------------------------------------" >> "$OUT_FILE"
        return
    fi

    # 检查是否有筛选条件
    if [ ${#parts[@]} -eq 1 ]; then
        # 没有筛选条件，统计总行数
        line_count=$(wc -l < "$csv_file")
        echo "总行数: $line_count" >> "$OUT_FILE"
    else
        # 有筛选条件，需要处理条件筛选
        echo "筛选条件:" >> "$OUT_FILE"

        # 读取CSV头部获取列索引
        header=$(head -n 1 "$csv_file")
        IFS=',' read -ra header_array <<< "$header"

        # 构建筛选条件
        local awk_condition=""
        local condition_desc=""

        for ((i=1; i<${#parts[@]}; i+=2)); do
            if [ $((i+1)) -lt ${#parts[@]} ]; then
                local column_name="${parts[i]}"
                local column_value="${parts[i+1]}"

                # 查找列索引
                local col_index=-1
                for ((j=0; j<${#header_array[@]}; j++)); do
                    if [ "${header_array[j]}" = "$column_name" ]; then
                        col_index=$((j+1))
                        break
                    fi
                done

                if [ $col_index -ne -1 ]; then
                    if [ -n "$awk_condition" ]; then
                        awk_condition="$awk_condition && "
                        condition_desc="$condition_desc, "
                    fi
                    awk_condition="$awk_condition\$$col_index==\"$column_value\""
                    condition_desc="$condition_desc$column_name=$column_value"
                    echo "  $column_name = $column_value (列$col_index)" >> "$OUT_FILE"
                else
                    echo "  警告: 列 '$column_name' 不存在" >> "$OUT_FILE"
                fi
            fi
        done

        if [ -n "$awk_condition" ]; then
            # 执行筛选并统计行数
            filtered_count=$(awk -F',' "NR>1 && $awk_condition {count++} END {print count+0}" "$csv_file")
            echo "筛选结果行数: $filtered_count" >> "$OUT_FILE"
        else
            echo "筛选条件无效，统计总行数" >> "$OUT_FILE"
            line_count=$(wc -l < "$csv_file")
            echo "总行数: $line_count" >> "$OUT_FILE"
        fi
    fi
    echo "----------------------------------------" >> "$OUT_FILE"
}

# 功能1：处理配置的CSV文件
process_csvs() {
    local csv_dir="$1"

    echo "=== CSV文件处理结果 ===" >> "$OUT_FILE"
    echo "CSV文件目录: $csv_dir" >> "$OUT_FILE"
    echo "" >> "$OUT_FILE"

    # 循环处理配置的CSV文件
    for csv_config in "${CMP_CSVS[@]}"; do
        parse_csv_config "$csv_config" "$csv_dir"
    done
    echo "" >> "$OUT_FILE"
}

# 功能2：处理Syslog文件
process_syslog() {
    local syslog_file="$1"
    
    if [ -f "$syslog_file" ]; then
        echo "=== Syslog文件处理结果 ===" >> "$OUT_FILE"
        echo "Syslog文件路径: $syslog_file" >> "$OUT_FILE"
        echo "包含'5 [BizEnv7]'的行:" >> "$OUT_FILE"
        echo "----------------------------------------" >> "$OUT_FILE"
        
        # 查找包含'5 [BizEnv7]'的行并输出到out文件
        grep "5 \[BizEnv7\]" "$syslog_file" >> "$OUT_FILE"
        
        echo "----------------------------------------" >> "$OUT_FILE"
        echo "" >> "$OUT_FILE"
    else
        echo "Syslog文件不存在: $syslog_file" >> "$OUT_FILE"
        echo "" >> "$OUT_FILE"
    fi
}

# 主函数
main() {
    # 检查参数数量
    if [ $# -ne 2 ]; then
        echo "使用方法: $0 <CSV文件目录> <Syslog文件路径>"
        echo "示例: $0 /path/to/csv_directory /path/to/Syslog.log"
        exit 1
    fi

    local csv_dir="$1"
    local syslog_file="$2"

    echo "开始处理文件..." >> "$OUT_FILE"
    echo "处理时间: $(date)" >> "$OUT_FILE"
    echo "" >> "$OUT_FILE"

    # 处理配置的CSV文件
    process_csvs "$csv_dir"

    # 处理Syslog文件
    process_syslog "$syslog_file"

    echo "处理完成！结果已保存到: $OUT_FILE"
}

# 执行主函数
main "$@"
