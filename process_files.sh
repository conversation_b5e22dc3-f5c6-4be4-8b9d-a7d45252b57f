#!/bin/bash

# Shell脚本：处理CSV文件和Syslog日志文件
# 功能1：读取指定的CSV文件并统计行数
# 功能2：读取Syslog文件中包含'5 [BizEnv7]'的行

# CSV文件目录配置
CSV_DIR="/path/to/csv/directory"

# CSV文件配置数组
# 格式1: 简单文件名 - "filename.csv"
# 格式2: 带条件筛选 - "filename.csv,column1,value1,column2,value2"
CMP_CSVS=(
    "1.csv"
    "2.csv,aa,1,bb,2"
    "3.csv"
    "4.csv,name,john,age,25"
    "5.csv,status,active,type,user"
    # 可以继续添加更多CSV文件...
)

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OUT_FILE="$SCRIPT_DIR/out"

# 清空输出文件
> "$OUT_FILE"

# 解析CSV配置并处理条件筛选
parse_csv_config() {
    local config="$1"

    # 分割配置字符串
    local csv_filename=$(echo "$config" | cut -d',' -f1)
    local csv_file="$CSV_DIR/$csv_filename"

    # 调试信息
    echo "DEBUG: csv_filename=$csv_filename"
    echo "DEBUG: CSV_DIR=$CSV_DIR"
    echo "DEBUG: csv_file=$csv_file"
    echo "DEBUG: OUT_FILE=$OUT_FILE"

    echo "文件名: $csv_filename" >> "$OUT_FILE"
    echo "完整路径: $csv_file" >> "$OUT_FILE"

    if [ ! -f "$csv_file" ]; then
        echo "状态: 文件不存在" >> "$OUT_FILE"
        echo "----------------------------------------" >> "$OUT_FILE"
        echo "DEBUG: 文件不存在，跳过处理"
        return
    fi

    echo "DEBUG: 文件存在，继续处理"

    # 检查是否有筛选条件 - 简单方式：看配置中是否包含逗号后还有内容
    local has_conditions=$(echo "$config" | grep -o ',' | wc -l)

    if [ "$has_conditions" -eq 0 ]; then
        # 没有筛选条件，统计总行数
        local line_count=$(wc -l < "$csv_file")
        echo "总行数: $line_count" >> "$OUT_FILE"
    else
        # 有筛选条件，使用简单的字符串处理方式
        echo "筛选条件:" >> "$OUT_FILE"

        # 读取CSV头部
        local header=$(head -n 1 "$csv_file")

        # 移除文件名部分，只保留条件部分
        local conditions_part=$(echo "$config" | sed 's/^[^,]*,//')
        echo "DEBUG: 条件部分: $conditions_part"

        # 简单处理：假设只有一对条件 column,value
        if [ "$has_conditions" -eq 2 ]; then
            # 只有一对条件：filename,column,value
            local column_name=$(echo "$conditions_part" | cut -d',' -f1)
            local column_value=$(echo "$conditions_part" | cut -d',' -f2)

            echo "DEBUG: 单条件 - $column_name = $column_value"

            # 查找列索引
            local col_index=$(echo "$header" | tr ',' '\n' | grep -n "^$column_name$" | cut -d':' -f1)

            if [ -n "$col_index" ]; then
                echo "  $column_name = $column_value (列$col_index)" >> "$OUT_FILE"
                local filtered_count=$(awk -v col="$col_index" -v val="$column_value" -F',' 'BEGIN{i=0}
                NR>1 && $col==val {i++} END{print i}' "$csv_file")
                echo "筛选结果行数: $filtered_count" >> "$OUT_FILE"
            else
                echo "  警告: 列 '$column_name' 不存在" >> "$OUT_FILE"
                local line_count=$(wc -l < "$csv_file")
                echo "总行数: $line_count" >> "$OUT_FILE"
            fi

        elif [ "$has_conditions" -eq 4 ]; then
            # 两对条件：filename,col1,val1,col2,val2
            local column1=$(echo "$conditions_part" | cut -d',' -f1)
            local value1=$(echo "$conditions_part" | cut -d',' -f2)
            local column2=$(echo "$conditions_part" | cut -d',' -f3)
            local value2=$(echo "$conditions_part" | cut -d',' -f4)

            echo "DEBUG: 双条件 - $column1 = $value1, $column2 = $value2"

            # 查找列索引
            local col_index1=$(echo "$header" | tr ',' '\n' | grep -n "^$column1$" | cut -d':' -f1)
            local col_index2=$(echo "$header" | tr ',' '\n' | grep -n "^$column2$" | cut -d':' -f1)

            if [ -n "$col_index1" ] && [ -n "$col_index2" ]; then
                echo "  $column1 = $value1 (列$col_index1)" >> "$OUT_FILE"
                echo "  $column2 = $value2 (列$col_index2)" >> "$OUT_FILE"
                # 改进的双条件awk命令
                local filtered_count=$(awk -v col1="$col_index1" -v val1="$value1" -v col2="$col_index2" -v val2="$value2" -F',' '
                BEGIN{i=0}
                NR>1 {
                    field1 = $col1
                    field2 = $col2
                    gsub(/[ \t\r\n]/, "", field1)
                    gsub(/[ \t\r\n]/, "", field2)
                    match1 = (field1 == val1 || ($col1+0) == (val1+0))
                    match2 = (field2 == val2 || ($col2+0) == (val2+0))
                    if(match1 && match2) i++
                }
                END{print i}' "$csv_file")
                echo "筛选结果行数: $filtered_count" >> "$OUT_FILE"
            else
                echo "  警告: 列不存在" >> "$OUT_FILE"
                local line_count=$(wc -l < "$csv_file")
                echo "总行数: $line_count" >> "$OUT_FILE"
            fi
        else
            echo "  暂不支持超过2对条件，统计总行数" >> "$OUT_FILE"
            local line_count=$(wc -l < "$csv_file")
            echo "总行数: $line_count" >> "$OUT_FILE"
        fi
    fi
    echo "----------------------------------------" >> "$OUT_FILE"
}

# 功能1：处理配置的CSV文件
process_csvs() {
    echo "=== CSV文件处理结果 ===" >> "$OUT_FILE"
    echo "CSV文件目录: $CSV_DIR" >> "$OUT_FILE"
    echo "" >> "$OUT_FILE"

    # 循环处理配置的CSV文件
    for csv_config in "${CMP_CSVS[@]}"; do
        parse_csv_config "$csv_config"
    done
    echo "" >> "$OUT_FILE"
}

# 功能2：处理Syslog文件
process_syslog() {
    local syslog_file="$1"
    
    if [ -f "$syslog_file" ]; then
        echo "=== Syslog文件处理结果 ===" >> "$OUT_FILE"
        echo "Syslog文件路径: $syslog_file" >> "$OUT_FILE"
        echo "包含'5 [BizEnv7]'的行:" >> "$OUT_FILE"
        echo "----------------------------------------" >> "$OUT_FILE"
        
        # 查找包含'5 [BizEnv7]'的行并输出到out文件
        grep "5 \[BizEnv7\]" "$syslog_file" >> "$OUT_FILE"
        
        echo "----------------------------------------" >> "$OUT_FILE"
        echo "" >> "$OUT_FILE"
    else
        echo "Syslog文件不存在: $syslog_file" >> "$OUT_FILE"
        echo "" >> "$OUT_FILE"
    fi
}

# 主函数
main() {
    # 检查参数数量
    if [ $# -ne 1 ]; then
        echo "使用方法: $0 <Syslog文件路径>"
        echo "示例: $0 /path/to/Syslog.log"
        exit 1
    fi

    local syslog_file="$1"

    echo "开始处理文件..." >> "$OUT_FILE"
    echo "处理时间: $(date)" >> "$OUT_FILE"
    echo "" >> "$OUT_FILE"

    # 处理配置的CSV文件
    process_csvs

    # 处理Syslog文件
    process_syslog "$syslog_file"

    echo "处理完成！结果已保存到: $OUT_FILE"
}

# 执行主函数
main "$@"
