#!/bin/bash

# 简化版本 - 避免所有复杂语法

# CSV文件目录配置
CSV_DIR="/path/to/csv/directory"

# 获取脚本所在目录
SCRIPT_DIR=`pwd`
OUT_FILE="$SCRIPT_DIR/out"

# 清空输出文件
echo "" > "$OUT_FILE"

# 处理单个CSV文件
process_single_csv() {
    local csv_file="$1"
    local config="$2"
    
    echo "处理文件: $csv_file" >> "$OUT_FILE"
    
    if [ ! -f "$csv_file" ]; then
        echo "文件不存在" >> "$OUT_FILE"
        echo "----------------------------------------" >> "$OUT_FILE"
        return
    fi
    
    # 简单统计行数
    local line_count=`wc -l < "$csv_file"`
    echo "总行数: $line_count" >> "$OUT_FILE"
    echo "----------------------------------------" >> "$OUT_FILE"
}

# 主处理函数
main() {
    if [ $# -ne 1 ]; then
        echo "使用方法: $0 <Syslog文件路径>"
        exit 1
    fi
    
    local syslog_file="$1"
    
    echo "开始处理..." >> "$OUT_FILE"
    echo "时间: `date`" >> "$OUT_FILE"
    echo "" >> "$OUT_FILE"
    
    # 处理CSV文件
    echo "=== CSV文件处理结果 ===" >> "$OUT_FILE"
    
    # 手动配置要处理的CSV文件
    process_single_csv "$CSV_DIR/1.csv" "1.csv"
    process_single_csv "$CSV_DIR/2.csv" "2.csv"
    # 继续添加更多文件...
    
    echo "" >> "$OUT_FILE"
    
    # 处理Syslog文件
    echo "=== Syslog文件处理结果 ===" >> "$OUT_FILE"
    if [ -f "$syslog_file" ]; then
        echo "Syslog文件: $syslog_file" >> "$OUT_FILE"
        echo "包含'5 [BizEnv7]'的行:" >> "$OUT_FILE"
        echo "----------------------------------------" >> "$OUT_FILE"
        grep "5 \[BizEnv7\]" "$syslog_file" >> "$OUT_FILE"
        echo "----------------------------------------" >> "$OUT_FILE"
    else
        echo "Syslog文件不存在: $syslog_file" >> "$OUT_FILE"
    fi
    
    echo "处理完成！结果保存到: $OUT_FILE"
}

main "$@"
