#!/bin/bash

# 测试awk命令的脚本 - 排错版本

CSV_FILE="test_data.csv"

echo "=== 测试数据 ==="
cat "$CSV_FILE"
echo ""

echo "=== 文件基本信息 ==="
echo "文件编码检查:"
file "$CSV_FILE"
echo "文件行数:"
wc -l "$CSV_FILE"
echo "文件前几行的十六进制显示:"
head -3 "$CSV_FILE" | od -c
echo ""

echo "=== 测试1: 单条件筛选 age=25 ==="
col_index=2
column_value="25"

echo "DEBUG: col_index=$col_index, column_value=$column_value"

# 先检查CSV结构
echo "CSV头部分析:"
awk -F',' 'NR==1 {for(i=1;i<=NF;i++) print "列" i ": [" $i "]"}' "$CSV_FILE"
echo ""

echo "所有数据行的第${col_index}列内容:"
awk -F',' "NR>1 {print \"行\" NR \": [\$${col_index}]=\" \$${col_index} \", 长度=\" length(\$${col_index})}" "$CSV_FILE"
echo ""

# 方法1：原始方法
echo "方法1 (原始字符串拼接):"
result1=$(awk -F',' "NR>1 && \$$col_index==\"$column_value\" {count++} END {print count+0}" "$CSV_FILE")
echo "结果: $result1"

# 方法2：使用-v参数
echo "方法2 (-v参数):"
result2=$(awk -v col="$col_index" -v val="$column_value" -F',' 'BEGIN{i=0} NR>1 && $col==val {i++} END{print i}' "$CSV_FILE")
echo "结果: $result2"

# 方法3：去除空格后比较
echo "方法3 (去除空格):"
result3=$(awk -v col="$col_index" -v val="$column_value" -F',' 'BEGIN{i=0} NR>1 {gsub(/[ \t\r\n]/, "", $col); if($col==val) i++} END{print i}' "$CSV_FILE")
echo "结果: $result3"

# 方法4：数值比较
echo "方法4 (数值比较):"
result4=$(awk -v col="$col_index" -v val="$column_value" -F',' 'BEGIN{i=0} NR>1 && ($col+0)==(val+0) {i++} END{print i}' "$CSV_FILE")
echo "结果: $result4"

# 方法5：详细调试
echo "方法5 (详细调试):"
awk -v col="$col_index" -v val="$column_value" -F',' '
BEGIN{i=0; print "开始处理，col=" col ", val=[" val "]"}
NR>1 {
    field_val = $col
    gsub(/[ \t\r\n]/, "", field_val)
    print "第" NR "行: 原值=[" $col "], 清理后=[" field_val "], 目标=[" val "], 字符串匹配:", (field_val==val ? "是" : "否"), ", 数值匹配:", (($col+0)==(val+0) ? "是" : "否")
    if(field_val==val) i++
}
END{print "最终计数: " i}' "$CSV_FILE"

echo ""
echo "=== 测试2: 双条件筛选 age=25 AND city=beijing ==="
# 模拟双条件：age=25 AND city=beijing
col_index1=2
value1="25"
col_index2=3
value2="beijing"

echo "DEBUG: col1=$col_index1, val1=$value1, col2=$col_index2, val2=$value2"

# 方法1：原始方法
echo "方法1 (原始):"
result3=$(awk -F',' "NR>1 && \$$col_index1==\"$value1\" && \$$col_index2==\"$value2\" {count++} END {print count+0}" "$CSV_FILE")
echo "结果: $result3"

# 方法2：使用-v参数
echo "方法2 (-v参数):"
result4=$(awk -v col1="$col_index1" -v val1="$value1" -v col2="$col_index2" -v val2="$value2" -F',' \
    'BEGIN{i=0} NR>1 && $col1==val1 && $col2==val2 {i++} END{print i}' "$CSV_FILE")
echo "结果: $result4"

# 方法3：调试版本
echo "方法3 (调试版本):"
awk -v col1="$col_index1" -v val1="$value1" -v col2="$col_index2" -v val2="$value2" -F',' '
BEGIN{i=0; print "开始处理，col1=" col1 ", val1=" val1 ", col2=" col2 ", val2=" val2} 
NR>1 {
    match1 = ($col1==val1)
    match2 = ($col2==val2)
    print "第" NR "行: $" col1 "=[" $col1 "], $" col2 "=[" $col2 "], 匹配1:", (match1 ? "是" : "否"), ", 匹配2:", (match2 ? "是" : "否"), ", 总匹配:", (match1 && match2 ? "是" : "否")
    if(match1 && match2) i++
} 
END{print "最终计数: " i}' "$CSV_FILE"
