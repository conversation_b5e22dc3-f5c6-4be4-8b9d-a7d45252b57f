#!/bin/bash

# 测试awk命令的脚本

CSV_FILE="test_data.csv"

echo "=== 测试数据 ==="
cat "$CSV_FILE"
echo ""

echo "=== 测试1: 单条件筛选 age=25 ==="
# 模拟单条件：age=25
col_index=2
column_value="25"

echo "DEBUG: col_index=$col_index, column_value=$column_value"

# 方法1：原始方法
echo "方法1 (原始):"
result1=$(awk -F',' "NR>1 && \$$col_index==\"$column_value\" {count++} END {print count+0}" "$CSV_FILE")
echo "结果: $result1"

# 方法2：使用-v参数
echo "方法2 (-v参数):"
result2=$(awk -v col="$col_index" -v val="$column_value" -F',' 'BEGIN{i=0} NR>1 && $col==val {i++} END{print i}' "$CSV_FILE")
echo "结果: $result2"

# 方法3：调试版本，显示每行处理过程
echo "方法3 (调试版本):"
awk -v col="$col_index" -v val="$column_value" -F',' '
BEGIN{i=0; print "开始处理，col=" col ", val=" val} 
NR>1 {
    print "第" NR "行: $" col "=[" $col "], 比较值=[" val "], 匹配:", ($col==val ? "是" : "否")
    if($col==val) i++
} 
END{print "最终计数: " i}' "$CSV_FILE"

echo ""
echo "=== 测试2: 双条件筛选 age=25 AND city=beijing ==="
# 模拟双条件：age=25 AND city=beijing
col_index1=2
value1="25"
col_index2=3
value2="beijing"

echo "DEBUG: col1=$col_index1, val1=$value1, col2=$col_index2, val2=$value2"

# 方法1：原始方法
echo "方法1 (原始):"
result3=$(awk -F',' "NR>1 && \$$col_index1==\"$value1\" && \$$col_index2==\"$value2\" {count++} END {print count+0}" "$CSV_FILE")
echo "结果: $result3"

# 方法2：使用-v参数
echo "方法2 (-v参数):"
result4=$(awk -v col1="$col_index1" -v val1="$value1" -v col2="$col_index2" -v val2="$value2" -F',' \
    'BEGIN{i=0} NR>1 && $col1==val1 && $col2==val2 {i++} END{print i}' "$CSV_FILE")
echo "结果: $result4"

# 方法3：调试版本
echo "方法3 (调试版本):"
awk -v col1="$col_index1" -v val1="$value1" -v col2="$col_index2" -v val2="$value2" -F',' '
BEGIN{i=0; print "开始处理，col1=" col1 ", val1=" val1 ", col2=" col2 ", val2=" val2} 
NR>1 {
    match1 = ($col1==val1)
    match2 = ($col2==val2)
    print "第" NR "行: $" col1 "=[" $col1 "], $" col2 "=[" $col2 "], 匹配1:", (match1 ? "是" : "否"), ", 匹配2:", (match2 ? "是" : "否"), ", 总匹配:", (match1 && match2 ? "是" : "否")
    if(match1 && match2) i++
} 
END{print "最终计数: " i}' "$CSV_FILE"
