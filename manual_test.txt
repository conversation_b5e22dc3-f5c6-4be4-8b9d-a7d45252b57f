# 手动测试awk命令

## 1. 创建测试CSV文件 (test.csv):
name,age,city,status
zhang,25,beijing,active
li,30,shanghai,inactive
wang,25,beijing,active
zhao,35,guangzhou,active
chen,25,she<PERSON><PERSON>,inactive

## 2. 测试命令 (在bash中执行):

# 基本信息检查
echo "=== 文件内容 ==="
cat test.csv

echo "=== 列信息 ==="
awk -F',' 'NR==1 {for(i=1;i<=NF;i++) print "列" i ": [" $i "]"}' test.csv

echo "=== 第2列(age)的所有值 ==="
awk -F',' 'NR>1 {print "行" NR ": [" $2 "], 长度=" length($2)}' test.csv

# 测试单条件筛选 age=25
echo "=== 测试: age=25 ==="

# 原始方法
echo "原始方法:"
awk -F',' 'NR>1 && $2=="25" {count++} END {print count+0}' test.csv

# 改进方法1: 去除空格
echo "去除空格:"
awk -F',' 'BEGIN{i=0} NR>1 {gsub(/[ \t\r\n]/, "", $2); if($2=="25") i++} END{print i}' test.csv

# 改进方法2: 数值比较
echo "数值比较:"
awk -F',' 'BEGIN{i=0} NR>1 && ($2+0)==25 {i++} END{print i}' test.csv

# 改进方法3: 综合方法
echo "综合方法:"
awk -F',' 'BEGIN{i=0} NR>1 {field=$2; gsub(/[ \t\r\n]/, "", field); if(field=="25" || ($2+0)==25) i++} END{print i}' test.csv

# 调试版本
echo "调试版本:"
awk -F',' 'NR>1 {field=$2; gsub(/[ \t\r\n]/, "", field); print "行" NR ": 原值=[" $2 "], 清理后=[" field "], 匹配25:", (field=="25" ? "是" : "否")}' test.csv

## 3. 测试双条件筛选 age=25 AND city=beijing

echo "=== 测试: age=25 AND city=beijing ==="

# 综合方法
echo "双条件综合方法:"
awk -F',' 'BEGIN{i=0} NR>1 {
    field1=$2; field2=$3; 
    gsub(/[ \t\r\n]/, "", field1); gsub(/[ \t\r\n]/, "", field2);
    match1=(field1=="25" || ($2+0)==25);
    match2=(field2=="beijing");
    if(match1 && match2) i++
} END{print i}' test.csv

# 调试版本
echo "双条件调试:"
awk -F',' 'NR>1 {
    field1=$2; field2=$3; 
    gsub(/[ \t\r\n]/, "", field1); gsub(/[ \t\r\n]/, "", field2);
    match1=(field1=="25"); match2=(field2=="beijing");
    print "行" NR ": age=[" field1 "], city=[" field2 "], 匹配1:", (match1?"是":"否"), ", 匹配2:", (match2?"是":"否")
}' test.csv

## 4. 期望结果:
# age=25 的行: 应该是 3 行 (zhang, wang, chen)
# age=25 AND city=beijing 的行: 应该是 2 行 (zhang, wang)
